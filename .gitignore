# =================================================================
#               Python 项目 .gitignore 文件
# =================================================================

# 依赖和虚拟环境
# -----------------------------------------------------------------
# 忽略存放项目依赖的虚拟环境文件夹
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 字节码和编译文件
# -----------------------------------------------------------------
# 忽略 Python 解释器生成的字节码和缓存文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
# -----------------------------------------------------------------
*.so

# 分发/打包
# -----------------------------------------------------------------
# 忽略打包和分发过程中生成的文件夹和文件
build/
dist/
*.egg-info/
*.egg
*.spec

# 单元测试/覆盖率报告
# -----------------------------------------------------------------
# 忽略测试工具和覆盖率报告生成的文件和文件夹
.coverage
.coverage.*
.cache
.pytest_cache/
.tox/
.nox/
htmlcov/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 数据库文件
# -----------------------------------------------------------------
# 忽略本地开发用的数据库文件
*.sqlite3
*.db

# 日志文件
# -----------------------------------------------------------------
# 忽略项目运行中产生的日志文件
*.log
logs/

# IDE 和编辑器配置
# -----------------------------------------------------------------
# 忽略各种IDE和文本编辑器自动生成的配置和临时文件
.idea/
.vscode/
.cursor/
*.swp
*.swo
*~
.augment

# 操作系统生成的文件
# -----------------------------------------------------------------
# 忽略不同操作系统自动生成的系统文件
.DS_Store          # macOS
Thumbs.db          # Windows
ehthumbs.db        # Windows
Desktop.ini        # Windows
$RECYCLE.BIN/      # Windows

# 项目特定忽略
# -----------------------------------------------------------------
# 忽略 OCTools 文件夹下的所有内容，除了 README.md
OCTools/
docs/
.rules/