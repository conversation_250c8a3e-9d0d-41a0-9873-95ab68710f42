#!/usr/bin/env python3
# -*- coding: utf-8 -*-

POWERSHELL_COMMANDS = {
    "执行策略设置": {
        "设置执行策略为Bypass": "Set-ExecutionPolicy Bypass -Scope LocalMachine -Force"
    },
    "电源管理": {
        "解锁所有电源选项": "Get-ChildItem 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings' -Recurse | Where-Object {$_.PSPath -notmatch '\\\\DefaultPowerSchemeValues|(\\\\[0-9]|\\\\255)$'} | ForEach-Object {Set-ItemProperty -Path $_.PSPath -Name 'Attributes' -Value 2 -Force}",
        "关闭允许节流状态": "powercfg /setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 3b04d4fd-1cc7-4f23-ab1c-d1337819c4bb 0; powercfg /setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 3b04d4fd-1cc7-4f23-ab1c-d1337819c4bb 0",
        "设置异类线程调度策略为所有处理器": "powercfg /setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 93b8b6dc-0698-4d1c-9ee4-0644e900c85d 0; powercfg /setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 93b8b6dc-0698-4d1c-9ee4-0644e900c85d 0",
        "设置异类短运行线程调度策略为所有处理器": "powercfg /setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bae08b81-2d5e-4688-ad6a-13243356654b 0; powercfg /setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bae08b81-2d5e-4688-ad6a-13243356654b 0",
    },
}
