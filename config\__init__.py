#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置模块统一导出

本模块统一导出所有配置文件中的配置字典，
提供统一的配置访问接口。
"""

# 导入各个配置模块的配置字典
from .appx_packages import APPX_PACKAGES
from .powershell_commands import POWERSHELL_COMMANDS
from .quick_tools import QUICK_TOOLS
from .registry_commands import REGISTRY_COMMANDS
from .system_cleanup import SYSTEM_CLEANUP

# 统一导出所有配置字典和工具函数
__all__ = [
    "APPX_PACKAGES",
    "POWERSHELL_COMMANDS",
    "QUICK_TOOLS",
    "REGISTRY_COMMANDS",
    "SYSTEM_CLEANUP",
]
