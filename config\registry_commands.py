#!/usr/bin/env python3
# -*- coding: utf-8 -*-

REGISTRY_COMMANDS = {
    "任务栏相关设置": {
        "任务栏时钟精确到秒": [
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "ShowSecondsInSystemClock" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_USERS\\DEFAULT\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "ShowSecondsInSystemClock" /t REG_DWORD /d 1 /f',
        ],
        "鼠标悬停在任务栏尾部时预览桌面": [
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "DisablePreviewDesktop" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_USERS\\DEFAULT\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "DisablePreviewDesktop" /t REG_DWORD /d 0 /f',
        ],
        "加快任务栏窗口预览显示速度": [
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Mouse" /v "MouseHoverTime" /t REG_SZ /d 100 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Control Panel\\Mouse" /v "MouseHoverTime" /t REG_SZ /d 100 /f',
        ],
        "显示任务栏中的动画": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "TaskbarAnimations" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "TaskbarAnimations" /t REG_DWORD /d 1 /f',
        ],
    },
    "Windows主题和外观设置": {
        "不要延时显示菜单": [
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "MenuShowDelay" /t REG_SZ /d 0 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Control Panel\\Desktop" /v "MenuShowDelay" /t REG_SZ /d 0 /f',
            'reg add "HKEY_USERS\\.DEFAULT\\Control Panel\\Desktop" /v "MenuShowDelay" /t REG_SZ /d 0 /f',
        ],
        "提高前台程序的显示速度": [
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "ForegroundLockTimeout" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Control Panel\\Desktop" /v "ForegroundLockTimeout" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_USERS\\.DEFAULT\\Control Panel\\Desktop" /v "ForegroundLockTimeout" /t REG_DWORD /d 0 /f',
        ],
        "平滑屏幕字体边缘": [
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "FontSmoothing" /t REG_SZ /d 2 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Control Panel\\Desktop" /v "FontSmoothing" /t REG_SZ /d 2 /f',
            'reg add "HKEY_USERS\\.DEFAULT\\Control Panel\\Desktop" /v "FontSmoothing" /t REG_SZ /d 2 /f',
        ],
    },
    "安全相关设置": {
        "UAC相关": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "PromptOnSecureDesktop" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "ConsentPromptBehaviorAdmin" /t REG_DWORD /d 0 /f',
        ],
        "允许在未登录的情况下关机": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "ShutdownWithoutLogon" /t REG_DWORD /d 1 /f',
        "内置管理员账户的管理审批模式": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "FilterAdministratorToken" /t REG_DWORD /d 1 /f',
        "关闭SmartScreen应用筛选器": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v "SmartScreenEnabled" /t REG_SZ /d off /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge\\PhishingFilter" /v "EnabledV9" /t REG_DWORD /d 0 /f',
        ],
        "关闭打开程序的安全警告": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Associations" /v "ModRiskFileTypes" /t REG_SZ /d ".bat;;.reg;.vbs;.chm;.msi;.js;.cmd" /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Associations" /v "ModRiskFileTypes" /t REG_SZ /d ".bat;;.reg;.vbs;.chm;.msi;.js;.cmd" /f',
            'reg add "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Associations" /v "ModRiskFileTypes" /t REG_SZ /d ".bat;;.reg;.vbs;.chm;.msi;.js;.cmd" /f',
        ],
        "禁用Windows Defender": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection" /v "DisableRealtimeMonitoring" /t REG_DWORD /d 1 /f',
        ],
        "禁用Windows Defender的云保护": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Spynet" /v "DisableBlockAtFirstSeen" /t REG_DWORD /d 1 /f',
        "禁用Windows Defender的自动样本提交": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Spynet" /v "DisableEnhancedNotifications" /t REG_DWORD /d 1 /f',
    },
    "「开始」菜单与Windows体验": {
        "不允许在「开始」菜单显示建议": [
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "SubscribedContent-338388Enabled" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "SubscribedContent-338389Enabled" /t REG_DWORD /d 1 /f',
        ],
        "关闭商店应用推广": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "PreInstalledAppsEnabled" /t REG_DWORD /d 0 /f'
        ],
        "关闭锁屏时的Windows聚焦推广": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "RotatingLockScreenEnable" /t REG_DWORD /d 0 /f',
            'Reg add "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "RotatingLockScreenEnabled" /t REG_DWORD /d "0" /f',
            'Reg add "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "RotatingLockScreenOverlayEnabled" /t REG_DWORD /d "0" /f',
        ],
        "关闭使用Windows时获取技巧和建议": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "SoftLandingEnabled" /t REG_DWORD /d 0 /f'
        ],
        "禁止自动安装推荐的应用程序": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager" /v "SilentInstalledAppsEnabled" /t REG_DWORD /d 0 /f'
        ],
        "关闭游戏录制工具": [
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR" /v "AppCaptureEnabled" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_CURRENT_USER\\System\\GameConfigStore" /v "GameDVR_Enabled" /t REG_DWORD /d 0 /f',
        ],
        "登录界面默认打开小键盘": 'reg add "HKCU\\Control Panel\\Keyboard" /v "InitialKeyboardIndicators" /t REG_SZ /d 2 /f',
        "加快关机速度": [
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "AutoEndTasks" /t REG_SZ /d 1 /f',
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "HungAppTimeout" /t REG_SZ /d 3000 /f',
            'reg add "HKEY_CURRENT_USER\\Control Panel\\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d 1000 /f',
        ],
        "缩短关闭服务等待时间": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d 1000 /f',
        "禁用应用后台运行": 'Reg add "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\BackgroundAccessApplications" /v "GlobalUserDisabled" /t REG_DWORD /d "1" /f',
        "关闭后台应用": 'Reg add "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Search" /v "BackgroundAppGlobalToggle" /t REG_DWORD /d "0" /f',
    },
    "资源管理器相关设置": {
        "打开资源管理器时显示此电脑": [
            'reg add "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced" /v "LaunchTo" /t REG_DWORD /d 1 /f'
        ],
        "Win11恢复经典样式的右键菜单": [
            'Reg add "HKCU\\Software\\Classes\\CLSID\\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}\\InprocServer32" /ve /t REG_SZ /d "-" /f'
        ],
        "优化Windows文件列表刷新策略": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer" /v "NoSimpleNetIDList" /t REG_DWORD /d 1 /f'
        ],
        "隐藏快捷方式小箭头": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Icons" /v 29 /d "%systemroot%\\system32\\imageres.dll,197" /t reg_sz /f'
        ],
        "隐藏可执行文件小盾牌": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Icons" /v 77 /d "%systemroot%\\system32\\imageres.dll,197" /t reg_sz /f'
        ],
        "创建快捷方式时不添加快捷方式字样": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Link" /v "ShowSuperHidden" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Link" /v "DisableKnownFolderLocationLinks" /t REG_DWORD /d 1 /f',
        ],
        "显示此电脑": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\HideDesktopIcons\\NewStartPanel" /v "{20D04FE0-3AEA-1069-A2D8-08002B30309D}" /t REG_DWORD /d 0 /f',
        "显示回收站": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\HideDesktopIcons\\NewStartPanel" /v "{645FF040-5081-101B-9F08-00AA002F954E}" /t REG_DWORD /d 0 /f',
        "显示控制面板": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\HideDesktopIcons\\NewStartPanel" /v "{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}" /t REG_DWORD /d 0 /f',
        "显示网络": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\HideDesktopIcons\\NewStartPanel" /v "{F02C1A0D-BE21-4350-88B0-7367FC96EF3C}" /t REG_DWORD /d 0 /f',
    },
    "Microsoft Edge 相关": {
        "跳过首次运行体验": 'Reg add "HKCU\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "HideFirstRunExperience" /t REG_DWORD /d "1" /f',
        "设置首次运行时不打开任何标签页": 'Reg add "HKCU\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "FirstRunTabs" /t REG_SZ /d "[]" /f',
        "禁用欢迎页面": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "DoNotShowWelcomePage" /t REG_DWORD /d "1" /f',
        "禁用首次运行欢迎页面": 'Reg add "HKCU\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "DoNotShowFirstRunExperience" /t REG_DWORD /d "1" /f',
        "禁用用户的首次欢迎界面和设置向导": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\OOBE" /v "DisableUserOutOfBoxExperience" /t REG_DWORD /d "1" /f',
        "禁用首次运行体验和欢迎页面": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Edge\\FirstRun" /v "DoNotShowFirstRunExperience" /t REG_DWORD /d "1" /f',
        "跳过首次运行的用户界面": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Edge\\FirstRun" /v "SkipFirstRunUI" /t REG_DWORD /d "1" /f',
        "关闭Adobe Flash即点即用": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge\\Security" /v "FlashClickToRunMode" /t REG_DWORD /d 0 /f',
        "Edge浏览器关闭后禁止继续运行后台应用": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "BackgroundModeEnabled" /t REG_DWORD /d 0 /f',
        "阻止Edge在后台运行": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Edge" /v "BackgroundModeEnabled" /t REG_DWORD /d "0" /f',
        "在后台关闭微软边缘-预加载-禁止Edge在后台运行-旧": [
            'Reg add "HKCU\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge" /v "AllowPrelaunch" /t REG_DWORD /d "0" /f',
            'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge" /v "AllowPrelaunch" /t REG_DWORD /d "0" /f',
            'Reg add "HKCU\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge\\Main" /v "AllowPrelaunch" /t REG_DWORD /d "0" /f',
            'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\MicrosoftEdge\\Main" /v "AllowPrelaunch" /t REG_DWORD /d "0" /f',
        ],
        "不检查下载程序的签名": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Internet Explorer\\Download" /v "CheckExeSignatures" /t REG_SZ /d "no" /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Internet Explorer\\Download" /v "CheckExeSignatures" /t REG_SZ /d "no" /f',
        ],
        "允许运行或安装软件，即使签名无效": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Internet Explorer\\Download" /v "RunInvalidSignatures" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Internet Explorer\\Download" /v "RunInvalidSignatures" /t REG_DWORD /d 1 /f',
        ],
        "不显示网页内容安全警告": [
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Zones" /v "SecuritySafe" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Zones" /v "" /t REG_SZ /d "" /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Zones" /v "SecuritySafe" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_USERS\\DEFAULT\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Zones" /v "" /t REG_SZ /d "" /f',
        ],
    },
    "Windows Update 相关": {
        "Windows更新不包括恶意软件删除工具": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\MRT" /v "DontOfferThroughWUAU" /t REG_DWORD /d 1 /f',
        "将Windows Update自动更新调整为从不检查": [
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update" /v "AUOptions" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update" /v "CachedAUOptions" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v "AUOptions" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v "NoAutoUpdate" /t REG_DWORD /d 1 /f',
        ],
    },
    "网络设置": {
        "关闭防火墙（标准/公用/域配置文件）": [
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\StandardProfile" /v "EnableFirewall" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\PublicProfile" /v "EnableFirewall" /t REG_DWORD /d 0 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\DomainProfile" /v "EnableFirewall" /t REG_DWORD /d 0 /f',
        ],
        "关闭远程协助": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Remote Assistance" /v "fAllowToGetHelp" /t REG_DWORD /d 0 /f',
    },
    "服务优化": {
        "禁用Windows server客户体验改善计划": 'Reg add "HKLM\\SOFTWARE\\Microsoft\\SQMClient\\Windows" /v "CEIPEnable" /t REG_DWORD /d "0" /f',
        "关闭windows客户体验改善计划": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\SQMClient\\Windows" /v "CEIPEnable" /t REG_DWORD /d "0" /f',
        "关闭windows messenger客户体验改善计划": 'Reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Messenger\\Client" /v "CEIP" /t REG_DWORD /d "2" /f',
        "禁止自动维护计划": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\ScheduledDiagnostics" /v "EnabledExecution" /t REG_DWORD /d 0 /f',
        "蓝屏时自动重启": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\CrashControl" /v "AutoReboot" /t REG_DWORD /d 1 /f',
        "关闭系统自动调试功能": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AeDebug" /v "Auto" /t REG_SZ /d 0 /f',
        "将磁盘错误检查的等待时间缩短到五秒": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager" /v "AutoChkTimeOut" /t REG_DWORD /d 5 /f',
        "禁用错误报告日志": [
            'reg add "HKEY_CURRENT_USER\\Software\\Policies\\Microsoft\\Windows\\Windows Error Reporting" /v "LoggingDisabled" /t REG_DWORD /d 1 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Error Reporting" /v "LoggingDisabled" /t REG_DWORD /d 1 /f',
        ],
        "禁止崩溃时写入调试信息": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\CrashControl" /v "CrashDumpEnabled" /t REG_DWORD /d 0 /f',
        "禁用账户登录日志报告": 'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon" /v "ReportBootOk" /t REG_SZ /d 0 /f',
        "禁用WfpDiag.ETL日志": [
            "netsh wfp set options netevents=off",
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\BFE\\Parameters\\Policy\\Options" /v "CollectNetEvents" /t REG_DWORD /d 0 /f',
        ],
    },
    "系统性能优化": {
        "启用大系统缓存": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 1 /f',
        "禁止系统内核与驱动程序分页到硬盘": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "DisablePagingExecutive" /t REG_DWORD /d 1 /f',
        "将文件管理系统缓存增加至256MB": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "IoPageLockLimit" /t REG_DWORD /d 10000000 /f',
        "关闭预读": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management\\PrefetchParameters" /v "EnablePrefetcher" /t REG_DWORD /d 0 /f',
        "关闭超级预读": 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management\\PrefetchParameters" /v "EnableSuperfetch" /t REG_DWORD /d 0 /f',
        "禁用处理器的幽灵和熔断补丁": [
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "FeatureSettingsOverride" /t REG_DWORD /d 3 /f',
            'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "FeatureSettingsOverrideMask" /t REG_DWORD /d 3 /f',
        ],
        "禁用内核隔离和虚拟化安全功能": [
            'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 0 /f',
            'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v RequireMicrosoftSignedComponentEnforcement /t REG_DWORD /d 0 /f',
            'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\CI\\Policy" /v SupportedPlatformState /t REG_DWORD /d 0 /f',
            'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\DeviceGuard" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 0 /f',
            'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\DeviceGuard" /v RequireMicrosoftSignedComponentEnforcement /t REG_DWORD /d 0 /f',
        ],
    },
}
