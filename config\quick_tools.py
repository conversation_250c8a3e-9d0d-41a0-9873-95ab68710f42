#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 快捷工具配置
# 此配置文件包含所有可在快捷工具视图中显示的工具

QUICK_TOOLS = {
    "系统管理工具": {
        "控制面板": {"command": "control"},
        "程序和功能": {"command": "appwiz.cpl"},
        "上帝模式": {"command": "explorer shell:::{ED7BA470-8E54-465E-825C-99712043E01C}"},
        "设备管理器": {"command": "devmgmt.msc"},
        "磁盘管理": {"command": "diskmgmt.msc"},
        "任务管理器": {"command": "taskmgr"},
        "组策略编辑器": {"command": "gpedit.msc"},
        "本地安全策略": {"command": "secpol.msc"},
        "服务管理": {"command": "services.msc"},
        "计算机管理": {"command": "compmgmt.msc"},
        "注册表编辑器": {"command": "regedit"},
        "系统配置": {"command": "msconfig"}
    },
    "系统设置": {
        "Windows设置": {"command": "start ms-settings:"},
        "网络设置": {"command": "start ms-settings:network"},
        "蓝牙设置": {"command": "start ms-settings:bluetooth"},
        "电源选项": {"command": "powercfg.cpl"},
        "性能选项": {"command": "SystemPropertiesPerformance"},
        "桌面图标设置": {"command": "rundll32.exe shell32.dll,Control_RunDLL desk.cpl,,0"},
        "Windows防火墙": {"command": "firewall.cpl"},
        "Internet选项": {"command": "inetcpl.cpl"},
        "网络连接": {"command": "ncpa.cpl"}
    },
    "诊断和监控": {
        "DirectX诊断工具": {"command": "dxdiag"},
        "资源监视器": {"command": "resmon"},
        "性能监视器": {"command": "perfmon"},
        "系统信息": {"command": "msinfo32"},
        "事件查看器": {"command": "eventvwr.msc"},
        "证书管理": {"command": "certmgr.msc"}
    },
    "系统维护": {
        "命令提示符": {"command": "cmd"},
        "PowerShell": {"command": "powershell"},
        "安全模式": {
            "command": "bcdedit /set {current} safeboot minimal",
            "description": "重启计算机后将进入安全模式，完成后自动恢复正常启动"
        }
    },
    "电源控制": {
        "重启": {"command": "shutdown /r /t 0"},
        "关机": {"command": "shutdown /s /t 0"},
        "睡眠": {"command": "rundll32.exe powrprof.dll,SetSuspendState 0,1,0"},
        "锁定计算机": {"command": "rundll32.exe user32.dll,LockWorkStation"}
    }
}
